{"level":"info","message":"Banner created successfully: cmf27r7y80000hxioy8<NAME_EMAIL>","timestamp":"2025-09-02 10:15:04"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:15:17"}
{"level":"info","message":"Discount with name Summer tyres created <NAME_EMAIL>","timestamp":"2025-09-02 10:16:01"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:16:20"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmelwhc3k01ii1ghx263l841y (07119915703)","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Starting updateProductPrice for product cmelwhc3k01ii1ghx263l841y","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Found product 07119915703 with base price 1.05","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Found active discount for 07119915703: PERCENTAGE with value 20","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"PERCENTAGE: 1.05 - 20% = 0.84","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Discount applied: 07119915703 - Original: 1.05, Final: 0.84, Discount %: 20","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Price changed for 07119915703: 1.05 -> 0.84","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Updated product 07119915703 with new price 0.84","timestamp":"2025-09-02 10:16:26"}
{"level":"error","message":"updateProductPrice error: TypeError: Cannot read properties of undefined (reading 'create')","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Failed","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmf27sfge0001hxiouwe8aqdg updated <NAME_EMAIL>","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:18:08"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:18:15"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:24:26"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:24:53"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:25:08"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:26:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:27:17"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:27:43"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:27:49"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:28:45"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:31"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:31"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:32"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:32"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:33"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:33"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:33"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:34"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:34"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:34"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:35"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:35"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:42"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:43"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:43"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:43"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:44"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:44"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:45"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:45"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:45"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:47"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:32:35"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:32:56"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:33:09"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:33:44"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:33:52"}
{"level":"info","message":"getProductAttributes -> Product \"07119915703\" has the attributes:  by <EMAIL>","timestamp":"2025-09-02 10:38:30"}
{"level":"info","message":"getProductAttributesHistory -> Product \"07119915703\" with <NAME_EMAIL>","timestamp":"2025-09-02 10:38:30"}
{"level":"info","message":"ProductAddAttributeAction -> Attribute \"culoare\" added successfully for product 07119915703 by <EMAIL>","timestamp":"2025-09-02 10:38:37"}
{"level":"info","message":"getProductAttributes -> Product \"07119915703\" has the attributes: culoare -- <NAME_EMAIL>","timestamp":"2025-09-02 10:38:37"}
{"level":"info","message":"getProductAttributesHistory -> Product \"07119915703\" with <NAME_EMAIL>","timestamp":"2025-09-02 10:38:37"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:41:51"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:44:24"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:44:59"}
{"level":"info","message":"Return cmf0xdah7000bhxt8ibx29s5k status updated from requested to <NAME_EMAIL>","timestamp":"2025-09-02 10:46:51"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 10:50:09"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-02 10:50:53"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes:  by <EMAIL>","timestamp":"2025-09-02 11:03:33"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with <NAME_EMAIL>","timestamp":"2025-09-02 11:03:33"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 11:03:37"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 11:27:25"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-02 11:34:04"}
{"level":"info","message":"Return cmezmlfkn001zhxhwuo6giixx status updated from requested to <NAME_EMAIL>","timestamp":"2025-09-02 11:40:48"}
{"level":"info","message":"User status changed for user cmf23ulcy0000hxg898o96w1q with changes: {\"isActive\":false,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-09-02 12:00:26"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 12:03:47"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 12:04:05"}
{"level":"info","message":"Banner created successfully: cmf2bqhc50006hxbsavnd2<NAME_EMAIL>","timestamp":"2025-09-02 12:06:28"}
{"level":"info","message":"User status changed for user cmf23ulcy0000hxg898o96w1q with changes: {\"isActive\":true,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-09-02 12:06:48"}
{"level":"info","message":"User status changed for user cmf23ulcy0000hxg898o96w1q with changes: {\"isActive\":true,\"isSuspended\":true,\"suspensionReason\":\"Administrative action\"} by <EMAIL>","timestamp":"2025-09-02 12:07:58"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 12:35:37"}
{"level":"info","message":"Group created: cmf2d5orw0000hxjgs6dhw79<NAME_EMAIL>","timestamp":"2025-09-02 12:46:17"}
{"level":"info","message":"User cmeqnrf1j001jhxhw3frmsw0d added to group cmf2d5orw0000hxjgs6dhw79<NAME_EMAIL>","timestamp":"2025-09-02 12:46:57"}
{"level":"info","message":"User cmeqnrf1j001jhxhw3frmsw0d details retrieved for userEmail","timestamp":"2025-09-02 12:47:06"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 12:53:57"}
{"level":"info","message":"Banner created successfully: cmf2dpe6q0003<NAME_EMAIL>","timestamp":"2025-09-02 13:01:37"}
{"level":"info","message":"Banner created successfully: cmf2du6kk0000hxi0f225rb7<NAME_EMAIL>","timestamp":"2025-09-02 13:05:20"}
{"level":"info","message":"Banner updated successfully: cmf2dpe6q0003<NAME_EMAIL>","timestamp":"2025-09-02 13:05:41"}
{"level":"info","message":"Banner created successfully: cmf2dw4sp0001hxi0m5h21990 by <EMAIL>","timestamp":"2025-09-02 13:06:51"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 13:46:58"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes:  by <EMAIL>","timestamp":"2025-09-02 13:51:37"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with <NAME_EMAIL>","timestamp":"2025-09-02 13:51:37"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes:  by <EMAIL>","timestamp":"2025-09-02 13:51:40"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with <NAME_EMAIL>","timestamp":"2025-09-02 13:51:40"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes:  by <EMAIL>","timestamp":"2025-09-02 13:51:40"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with <NAME_EMAIL>","timestamp":"2025-09-02 13:51:40"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes:  by <EMAIL>","timestamp":"2025-09-02 13:51:40"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with <NAME_EMAIL>","timestamp":"2025-09-02 13:51:40"}
{"level":"info","message":"ProductAddAttributeAction -> Attribute \"culoare\" added successfully for product 11531743192 by <EMAIL>","timestamp":"2025-09-02 13:51:53"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes: culoare -- <NAME_EMAIL>","timestamp":"2025-09-02 13:51:53"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with <NAME_EMAIL>","timestamp":"2025-09-02 13:51:53"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 13:53:47"}
{"level":"info","message":"User cmf23ulcy0000hxg898o96w1q details retrieved for userEmail","timestamp":"2025-09-02 13:56:18"}
{"level":"info","message":"User cmf23ulcy0000hxg898o96w1q added to group cmf2d5orw0000hxjgs6dhw79<NAME_EMAIL>","timestamp":"2025-09-02 13:58:02"}
{"level":"info","message":"Return cmf2g1cqo000ehxq0un2rzw4w status updated from requested to <NAME_EMAIL>","timestamp":"2025-09-02 14:09:27"}
{"level":"info","message":"Discount with name Test created <NAME_EMAIL>","timestamp":"2025-09-02 14:17:26"}
{"level":"error","message":"DiscountAddProductAction -> Product \"07119915703\" already has a <NAME_EMAIL>","timestamp":"2025-09-02 14:17:48"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmelwhb1x006t1ghx6aoh3v26 (07119963155)","timestamp":"2025-09-02 14:18:04"}
{"level":"info","message":"Starting updateProductPrice for product cmelwhb1x006t1ghx6aoh3v26","timestamp":"2025-09-02 14:18:04"}
{"level":"info","message":"Found product 07119963155 with base price 39.28","timestamp":"2025-09-02 14:18:04"}
{"level":"info","message":"Found active discount for 07119963155: NEW_PRICE with value 54","timestamp":"2025-09-02 14:18:04"}
{"level":"info","message":"Discount not applied: 07119963155 - Final price would not be lower","timestamp":"2025-09-02 14:18:04"}
{"level":"info","message":"No price change for 07119963155: 39.28","timestamp":"2025-09-02 14:18:04"}
{"level":"info","message":"Updated product 07119963155 with new price 39.28","timestamp":"2025-09-02 14:18:04"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-09-02 14:18:04"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmf2gewg4000khxi0m2voq2y8 updated <NAME_EMAIL>","timestamp":"2025-09-02 14:18:04"}
{"level":"info","message":"Discount with name Test2 created <NAME_EMAIL>","timestamp":"2025-09-02 14:20:15"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmelwhayh00061ghx9q0d1o34 (01200426532)","timestamp":"2025-09-02 14:20:31"}
{"level":"info","message":"Starting updateProductPrice for product cmelwhayh00061ghx9q0d1o34","timestamp":"2025-09-02 14:20:31"}
{"level":"info","message":"Found product 01200426532 with base price 4.84","timestamp":"2025-09-02 14:20:31"}
{"level":"info","message":"Found active discount for 01200426532: FIXED_AMOUNT with value 10","timestamp":"2025-09-02 14:20:31"}
{"level":"info","message":"FIXED_AMOUNT: 4.84 - 10 = 0","timestamp":"2025-09-02 14:20:31"}
{"level":"info","message":"Discount applied: 01200426532 - Original: 4.84, Final: 0, Discount %: 100","timestamp":"2025-09-02 14:20:31"}
{"level":"info","message":"Price changed for 01200426532: 4.84 -> 0","timestamp":"2025-09-02 14:20:31"}
{"level":"info","message":"Updated product 01200426532 with new price 0","timestamp":"2025-09-02 14:20:31"}
{"level":"error","message":"updateProductPrice error: TypeError: Cannot read properties of undefined (reading 'create')","timestamp":"2025-09-02 14:20:31"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Failed","timestamp":"2025-09-02 14:20:31"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmf2giit4000rhxi04j0cmwb6 updated <NAME_EMAIL>","timestamp":"2025-09-02 14:20:31"}
{"level":"error","message":"DiscountAddProductAction -> Product \"07119915703\" already has a <NAME_EMAIL>","timestamp":"2025-09-02 14:21:01"}
