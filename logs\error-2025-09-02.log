{"level":"error","message":"updateProductPrice error: TypeError: Cannot read properties of undefined (reading 'create')","timestamp":"2025-09-02 10:16:26"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 10:50:09"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 11:03:37"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 11:27:25"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 12:03:47"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 12:35:37"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 12:53:57"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 13:53:47"}
{"level":"error","message":"DiscountAddProductAction -> Product \"07119915703\" already has a <NAME_EMAIL>","timestamp":"2025-09-02 14:17:48"}
{"level":"error","message":"updateProductPrice error: TypeError: Cannot read properties of undefined (reading 'create')","timestamp":"2025-09-02 14:20:31"}
{"level":"error","message":"DiscountAddProductAction -> Product \"07119915703\" already has a <NAME_EMAIL>","timestamp":"2025-09-02 14:21:01"}
